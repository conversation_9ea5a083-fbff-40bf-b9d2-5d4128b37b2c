<template>
  <div class="agent-setting">
    <!-- 整合的头部区域 -->
    <div class="header-container">
      <!-- 筛选条件 -->
      <div class="filter-section">
        <div class="filter-header">

          <span class="filter-title">筛选条件</span>
          <div>
            <el-button type="primary" icon="el-icon-plus" @click="handleCreateAgent">
              新建智能体
            </el-button>
            <el-button type="default" icon="el-icon-setting" @click="handleAuthManage">
              授权管理
            </el-button>
          </div>
        </div>
        <el-form :inline="true" :model="filterForm" class="filter-form">
          <el-form-item label="智能体大类">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索智能体..."
              prefix-icon="el-icon-search"
              class="search-input"
              @keyup.enter="handleSearch"
              clearable
            />
          </el-form-item>
          <el-form-item label="智能体大类">
            <el-select v-model="filterForm.category" placeholder="全部" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="适用助手" value="适用助手"></el-option>
              <el-option label="课程作业" value="课程作业"></el-option>
              <el-option label="数学设计" value="数学设计"></el-option>
              <el-option label="学情分析" value="学情分析"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="智能体子类">
            <el-select v-model="filterForm.subCategory" placeholder="全部" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="智能助手" value="智能助手"></el-option>
              <el-option label="作业助手" value="作业助手"></el-option>
              <el-option label="公开课设计" value="公开课设计"></el-option>
              <el-option label="数学设计" value="数学设计"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="所属学科">
            <el-select v-model="filterForm.subject" placeholder="全部" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="数学" value="数学"></el-option>
              <el-option label="英语" value="英语"></el-option>
              <el-option label="物理" value="物理"></el-option>
              <el-option label="化学" value="化学"></el-option>
              <el-option label="语文" value="语文"></el-option>
              <el-option label="历史" value="历史"></el-option>
              <el-option label="地理" value="地理"></el-option>
              <el-option label="音乐" value="音乐"></el-option>
              <el-option label="计算机" value="计算机"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="所属年段">
            <el-select v-model="filterForm.type" placeholder="全部" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="初中" value="初中"></el-option>
              <el-option label="高中" value="高中"></el-option>
              <el-option label="小学" value="小学"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item class="filter-actions">
            <el-button type="primary" icon="el-icon-search" @click="handleFilter">筛选</el-button>
            <el-button icon="el-icon-refresh" @click="handleReset">重置</el-button>

          </el-form-item>
        </el-form>
      </div>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-table
        :data="tableData"
        v-loading="loading"
        border
        stripe
        style="width: 100%"
        :header-cell-style="{ background: '#f5f7f9', fontWeight: 'bold', textAlign: 'center' }"
        :cell-style="{ textAlign: 'center' }"
      >
        <el-table-column type="index" label="序号" width="60" align="center" />

        <el-table-column label="LOGO" width="80" align="center">
          <template slot-scope="scope">
            <div class="logo-cell">
              <img v-if="scope.row.logo" :src="scope.row.logo" alt="logo" class="agent-logo" />
              <div v-else class="default-logo">
                <i :class="scope.row.defaultIcon || 'el-icon-user'"></i>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column prop="name" label="应用名称" min-width="150" align="center" />

        <el-table-column prop="category" label="智能体大类" width="120" align="center" />

        <el-table-column prop="subCategory" label="智能体子类" width="120" align="center" />

        <el-table-column prop="subject" label="所属学科" width="100" align="center" />

        <el-table-column prop="type" label="所属年段" width="100" align="center" />

        <el-table-column prop="creator" label="创建时间" width="120" align="center" />

        <el-table-column prop="createTime" label="状态" width="100" align="center">
          <template slot-scope="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ scope.row.status }}
            </el-tag>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="250" align="center" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button type="text" size="small" @click="handleDelete(scope.row)">删除</el-button>
            <el-button type="text" size="small" @click="handleAuth(scope.row)" style="color: #409eff;">授权</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 分页 -->
    <div class="pagination-section">
      <div class="pagination-info">
        共 {{ total }} 条，共 {{ Math.ceil(total / pageSize) }} 页
      </div>
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pageSize"
        layout="sizes, prev, pager, next, jumper"
        :total="total"
        background
      />
    </div>

    <!-- 新建智能体对话框 -->
    <CreateAgentDialog
      :visible.sync="createDialogVisible"
      @confirm="handleCreateConfirm"
      @close="handleCreateClose"
    />

    <!-- 智能体授权对话框 -->
    <AgentAuthDialog
      :visible.sync="authDialogVisible"
      :agent-data="currentAgent"
      @confirm="handleAuthConfirm"
      @close="handleAuthClose"
    />
  </div>
</template>

<script>
import CreateAgentDialog from './components/CreateAgentDialog.vue'
import AgentAuthDialog from './components/AgentAuthDialog.vue'

export default {
  name: 'AgentSetting',
  components: {
    CreateAgentDialog,
    AgentAuthDialog
  },
  data() {
    return {
      // 搜索关键词
      searchKeyword: '',

      // 筛选表单
      filterForm: {
        category: '',
        subCategory: '',
        subject: '',
        type: ''
      },

      // 表格数据
      tableData: [],
      loading: false,

      // 分页
      currentPage: 1,
      pageSize: 10,
      total: 0,

      // 新建对话框
      createDialogVisible: false,

      // 授权对话框
      authDialogVisible: false,
      currentAgent: {},

      // 模拟数据
      mockData: [
        {
          id: 1,
          name: '数学教学助手',
          category: '适用助手',
          subCategory: '智能助手',
          subject: '数学',
          type: '初中',
          creator: '2023-05-15',
          status: '启用',
          logo: '',
          defaultIcon: 'el-icon-edit'
        },
        {
          id: 2,
          name: '英语写作助手',
          category: '课程作业',
          subCategory: '作业助手',
          subject: '英语',
          type: '高中',
          creator: '2023-06-20',
          status: '删除',
          logo: '',
          defaultIcon: 'el-icon-document'
        },
        {
          id: 3,
          name: '物理实验助手',
          category: '数学设计',
          subCategory: '公开课设计',
          subject: '物理',
          type: '高中',
          creator: '2023-07-10',
          status: '启用',
          logo: '',
          defaultIcon: 'el-icon-cpu'
        },
        {
          id: 4,
          name: '化学方程式生成',
          category: '适用助手',
          subCategory: '智能助手',
          subject: '化学',
          type: '高中',
          creator: '2023-08-05',
          status: '启用',
          logo: '',
          defaultIcon: 'el-icon-magic-stick'
        },
        {
          id: 5,
          name: '历史知识梳理',
          category: '数学设计',
          subCategory: '数学设计',
          subject: '历史',
          type: '初中',
          creator: '2023-09-12',
          status: '启用',
          logo: '',
          defaultIcon: 'el-icon-collection'
        },
        {
          id: 6,
          name: '生物实验指导',
          category: '数学设计',
          subCategory: '数学设计',
          subject: '生物',
          type: '高中',
          creator: '2023-10-18',
          status: '启用',
          logo: '',
          defaultIcon: 'el-icon-grape'
        },
        {
          id: 7,
          name: '地理知识问答',
          category: '学情分析',
          subCategory: '学情分析',
          subject: '地理',
          type: '高中',
          creator: '2023-11-22',
          status: '启用',
          logo: '',
          defaultIcon: 'el-icon-location'
        },
        {
          id: 8,
          name: '语文阅读理解',
          category: '课程作业',
          subCategory: '作业助手',
          subject: '语文',
          type: '小学',
          creator: '2023-12-05',
          status: '删除',
          logo: '',
          defaultIcon: 'el-icon-reading'
        },
        {
          id: 9,
          name: '编程学习助手',
          category: '适用助手',
          subCategory: '适用推荐',
          subject: '计算机',
          type: '高中',
          creator: '2024-01-15',
          status: '启用',
          logo: '',
          defaultIcon: 'el-icon-monitor'
        },
        {
          id: 10,
          name: '音乐理论指导',
          category: '数学设计',
          subCategory: '数学设计',
          subject: '音乐',
          type: '小学',
          creator: '2024-02-20',
          status: '启用',
          logo: '',
          defaultIcon: 'el-icon-headset'
        }
      ]
    }
  },

  mounted() {
    this.loadData()
  },
  methods: {
    // 加载数据
    loadData() {
      this.loading = true

      // 模拟API调用
      setTimeout(() => {
        let filteredData = [...this.mockData]

        // 应用搜索过滤
        if (this.searchKeyword) {
          filteredData = filteredData.filter(item =>
            item.name.includes(this.searchKeyword)
          )
        }

        // 应用筛选条件
        if (this.filterForm.category) {
          filteredData = filteredData.filter(item =>
            item.category === this.filterForm.category
          )
        }

        if (this.filterForm.subCategory) {
          filteredData = filteredData.filter(item =>
            item.subCategory === this.filterForm.subCategory
          )
        }

        if (this.filterForm.subject) {
          filteredData = filteredData.filter(item =>
            item.subject === this.filterForm.subject
          )
        }

        if (this.filterForm.type) {
          filteredData = filteredData.filter(item =>
            item.type === this.filterForm.type
          )
        }

        // 设置总数
        this.total = filteredData.length

        // 分页处理
        const start = (this.currentPage - 1) * this.pageSize
        const end = start + this.pageSize
        this.tableData = filteredData.slice(start, end)

        this.loading = false
      }, 500)
    },

    // 搜索
    handleSearch() {
      this.currentPage = 1
      this.loadData()
    },

    // 筛选
    handleFilter() {
      this.currentPage = 1
      this.loadData()
    },

    // 重置
    handleReset() {
      this.searchKeyword = ''
      this.filterForm = {
        category: '',
        subCategory: '',
        subject: '',
        type: ''
      }
      this.currentPage = 1
      this.loadData()
    },

    // 新建智能体
    handleCreateAgent() {
      this.createDialogVisible = true
    },

    // 授权管理
    handleAuthManage() {
      this.$message.info('授权管理功能开发中...')
    },

    // 处理创建确认
    handleCreateConfirm(formData) {
      // 生成新的ID
      const newId = Math.max(...this.mockData.map(item => item.id)) + 1

      // 创建新的智能体数据
      const newAgent = {
        id: newId,
        name: formData.name,
        category: formData.category,
        subCategory: formData.subCategory,
        subject: formData.subject,
        type: formData.type,
        creator: new Date().toISOString().split('T')[0],
        status: '启用',
        logo: formData.logoUrl,
        defaultIcon: this.getDefaultIcon(formData.category),
        description: formData.description,
        model: formData.model,
        systemPrompt: formData.systemPrompt
      }

      // 添加到模拟数据中
      this.mockData.unshift(newAgent)

      this.$message.success('智能体创建成功！')

      // 刷新数据
      this.currentPage = 1
      this.loadData()
    },

    // 处理创建关闭
    handleCreateClose() {
      this.createDialogVisible = false
    },

    // 获取默认图标
    getDefaultIcon(category) {
      const iconMap = {
        '适用助手': 'el-icon-user',
        '课程作业': 'el-icon-document',
        '数学设计': 'el-icon-cpu',
        '学情分析': 'el-icon-data-analysis'
      }
      return iconMap[category] || 'el-icon-star-off'
    },

    // 编辑
    handleEdit(row) {
      this.$message.info(`编辑智能体: ${row.name}`)
    },

    // 删除
    handleDelete(row) {
      this.$confirm(`确定要删除智能体 "${row.name}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.loadData()
      }).catch(() => {
        this.$message.info('已取消删除')
      })
    },

    // 复制
    handleCopy(row) {
      this.$message.info(`复制智能体: ${row.name}`)
    },

    // 授权
    handleAuth(row) {
      this.currentAgent = row
      this.authDialogVisible = true
    },

    // 处理授权确认
    handleAuthConfirm(authData) {
      console.log('授权数据:', authData)
      this.$message.success(`智能体 "${this.currentAgent.name}" 授权成功！`)
      // 这里可以调用API保存授权信息
    },

    // 处理授权关闭
    handleAuthClose() {
      this.authDialogVisible = false
      this.currentAgent = {}
    },

    // 获取状态类型
    getStatusType(status) {
      switch (status) {
        case '启用':
          return 'success'
        case '删除':
          return 'danger'
        default:
          return 'info'
      }
    },

    // 分页大小改变
    handleSizeChange(val) {
      this.pageSize = val
      this.currentPage = 1
      this.loadData()
    },

    // 当前页改变
    handleCurrentChange(val) {
      this.currentPage = val
      this.loadData()
    }
  }
}
</script>
<style lang="less" scoped>
.agent-setting {
  background-color: #f5f5f5;

  .header-container {
    margin-bottom: 20px;
    background: white;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    overflow: hidden;

    .title-section {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 24px 24px 20px 24px;
      border-bottom: 1px solid #f0f0f0;

      .title-left {
        .page-title {
          margin: 0 0 4px 0;
          font-size: 28px;
          font-weight: 600;
          color: #1a1a1a;
          line-height: 1.2;
        }

        .title-desc {
          font-size: 14px;
          color: #666;
          margin: 0;
        }
      }

      .title-actions {
        display: flex;
        align-items: center;
        gap: 12px;

        .search-input {
          width: 280px;

          /deep/ .el-input__inner {
            border-radius: 8px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;

            &:focus {
              border-color: #409eff;
              box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
            }
          }
        }

        .el-button {
          border-radius: 8px;
          font-weight: 500;
          padding: 10px 20px;

          &.el-button--primary {
            background: linear-gradient(135deg, #409eff 0%, #5dade2 100%);
            border: none;

            &:hover {
              background: linear-gradient(135deg, #66b1ff 0%, #7fb8e5 100%);
            }
          }

          &.el-button--default {
            border-color: #d9d9d9;
            color: #666;

            &:hover {
              border-color: #409eff;
              color: #409eff;
            }
          }
        }
      }
    }

    .filter-section {
      padding: 20px 24px 24px 24px;

      .filter-header {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
        justify-content:space-between;
        .el-icon-filter {
          font-size: 16px;
          color: #409eff;
          margin-right: 8px;
        }

        .filter-title {
          font-size: 16px;
          font-weight: 500;
          color: #333;
        }
      }

      .filter-form {
        .el-form-item {
          margin-bottom: 0;
          margin-right: 24px;

          &.filter-actions {
            margin-right: 0;
            margin-left: auto;
          }

          &:last-child {
            margin-right: 0;
          }
        }

        .el-form-item__label {
          font-weight: 500;
          color: #555;
          font-size: 14px;
        }

        .el-select {
          width: 140px;

          /deep/ .el-input__inner {
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            transition: all 0.3s ease;

            &:focus {
              border-color: #409eff;
            }
          }
        }

        .el-button {
          border-radius: 6px;
          font-weight: 500;

          &.el-button--primary {
            background: #409eff;
            border-color: #409eff;

            &:hover {
              background: #66b1ff;
              border-color: #66b1ff;
            }
          }
        }
      }
    }
  }

  .table-section {
    margin-bottom: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .logo-cell {
      display: flex;
      justify-content: center;
      align-items: center;

      .agent-logo {
        width: 32px;
        height: 32px;
        border-radius: 4px;
        object-fit: cover;
      }

      .default-logo {
        width: 32px;
        height: 32px;
        background: #f0f0f0;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #999;
        font-size: 16px;
      }
    }

    .el-table {
      .el-button--text {
        padding: 0;
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .pagination-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    .pagination-info {
      color: #666;
      font-size: 14px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .agent-setting {
    .header-container {
      .filter-section {
        .filter-form {
          .el-form-item {
            margin-right: 16px;
            margin-bottom: 12px;
          }

          .el-select {
            width: 120px;
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .agent-setting {
    padding: 12px;

    .header-container {
      border-radius: 8px;

      .title-section {
        flex-direction: column;
        gap: 16px;
        align-items: stretch;
        padding: 20px 16px 16px 16px;

        .title-left {
          text-align: center;

          .page-title {
            font-size: 24px;
          }
        }

        .title-actions {
          justify-content: center;
          flex-wrap: wrap;
          gap: 8px;

          .search-input {
            width: 100%;
            max-width: 280px;
          }

          .el-button {
            flex: 1;
            min-width: 120px;
          }
        }
      }

      .filter-section {
        padding: 16px;

        .filter-form {
          .el-form-item {
            margin-right: 8px;
            margin-bottom: 12px;
            width: calc(50% - 4px);

            &.filter-actions {
              width: 100%;
              margin-left: 0;
              text-align: center;
              margin-top: 8px;
            }
          }

          .el-select {
            width: 100%;
          }

          .filter-actions {
            .el-button {
              margin: 0 4px;
            }
          }
        }
      }
    }

    .pagination-section {
      flex-direction: column;
      gap: 16px;
      align-items: center;
      padding: 16px;
    }
  }
}

@media (max-width: 480px) {
  .agent-setting {
    padding: 8px;

    .header-container {
      .title-section {
        .title-actions {
          .el-button {
            font-size: 12px;
            padding: 8px 12px;
          }
        }
      }

      .filter-section {
        .filter-form {
          .el-form-item {
            width: 100%;
            margin-right: 0;
          }
        }
      }
    }
  }
}
</style>
