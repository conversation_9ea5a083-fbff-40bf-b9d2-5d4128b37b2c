<template>
  <el-dialog
    title="智能体授权"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="agent-auth-dialog"
    @close="handleClose"
  >
    <!-- 智能体信息展示 -->
    <div class="agent-info">
      <div class="agent-icon">
        <img v-if="agentData.logo" :src="agentData.logo" alt="logo" class="agent-logo" />
        <div v-else class="default-logo">
          <i :class="agentData.defaultIcon || 'el-icon-user'"></i>
        </div>
      </div>
      <div class="agent-details">
        <h3 class="agent-name">{{ agentData.name || '数学解题助手' }}</h3>
        <p class="agent-desc">{{ agentData.description || '通用助手 - 智能问答' }}</p>
      </div>
    </div>

    <!-- 授权表单 -->
    <el-form
      ref="authForm"
      :model="form"
      :rules="rules"
      label-width="80px"
      class="auth-form"
    >
      <!-- 授权说明 -->
      <div class="auth-notice">
        <i class="el-icon-info"></i>
        <span>请选择授权范围和有效期，授权后该智能体将在指定时间内对选定区域开放使用。</span>
      </div>
      <!-- 区县选择 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="区县" prop="district">
            <el-select
              v-model="form.district"
              placeholder="请选择区县"
              style="width: 100%"
              @change="handleDistrictChange"
            >
              <el-option
                v-for="item in districtOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="学校" prop="school">
            <el-select
              v-model="form.school"
              placeholder="请选择学校"
              style="width: 100%"
              :disabled="!form.district"
            >
              <el-option
                v-for="item in schoolOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 时间选择 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="开始时间" prop="startTime">
            <el-date-picker
              v-model="form.startTime"
              type="datetime"
              placeholder="年/月/日 --:--"
              style="width: 100%"
              format="yyyy/MM/dd HH:mm"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="startTimeOptions"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="结束时间" prop="endTime">
            <el-date-picker
              v-model="form.endTime"
              type="datetime"
              placeholder="年/月/日 --:--"
              style="width: 100%"
              format="yyyy/MM/dd HH:mm"
              value-format="yyyy-MM-dd HH:mm:ss"
              :picker-options="endTimeOptions"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <!-- 底部按钮 -->
    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确认授权
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AgentAuthDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    agentData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      form: {
        district: '',
        school: '',
        startTime: '',
        endTime: ''
      },
      rules: {
        district: [
          { required: true, message: '请选择区县', trigger: 'change' }
        ],
        school: [
          { required: true, message: '请选择学校', trigger: 'change' }
        ],
        startTime: [
          { required: true, message: '请选择开始时间', trigger: 'change' }
        ],
        endTime: [
          { required: true, message: '请选择结束时间', trigger: 'change' }
        ]
      },
      // 区县选项
      districtOptions: [
        { label: '海淀区', value: 'haidian' },
        { label: '朝阳区', value: 'chaoyang' },
        { label: '西城区', value: 'xicheng' },
        { label: '东城区', value: 'dongcheng' },
        { label: '丰台区', value: 'fengtai' },
        { label: '石景山区', value: 'shijingshan' }
      ],
      // 学校选项
      schoolOptions: [],
      // 时间选择器选项
      startTimeOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        }
      },
      endTimeOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        }
      }
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.resetForm()
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.form = {
        district: '',
        school: '',
        startTime: '',
        endTime: ''
      }
      this.schoolOptions = []
      if (this.$refs.authForm) {
        this.$refs.authForm.clearValidate()
      }
    },

    // 区县变化处理
    handleDistrictChange(value) {
      this.form.school = ''
      this.loadSchoolOptions(value)
    },

    // 加载学校选项
    loadSchoolOptions(district) {
      // 模拟根据区县加载学校数据
      const schoolMap = {
        haidian: [
          { label: '清华大学附属中学', value: 'qinghua' },
          { label: '北京大学附属中学', value: 'beida' },
          { label: '人民大学附属中学', value: 'renda' }
        ],
        chaoyang: [
          { label: '朝阳外国语学校', value: 'chaoyang_foreign' },
          { label: '北京工业大学附属中学', value: 'bjut' }
        ],
        xicheng: [
          { label: '北京四中', value: 'beijing4' },
          { label: '北京八中', value: 'beijing8' }
        ]
      }
      this.schoolOptions = schoolMap[district] || []
    },

    // 关闭对话框
    handleClose() {
      this.resetForm()
      this.$emit('close')
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    },

    // 确认授权
    handleConfirm() {
      this.$refs.authForm.validate((valid) => {
        if (valid) {
          // 验证时间逻辑
          if (new Date(this.form.startTime) >= new Date(this.form.endTime)) {
            this.$message.error('结束时间必须大于开始时间')
            return false
          }

          this.loading = true

          // 模拟API调用
          setTimeout(() => {
            this.loading = false
            this.$message.success('授权成功！')
            this.$emit('confirm', {
              agentId: this.agentData.id,
              ...this.form
            })
            this.dialogVisible = false
            this.resetForm()
          }, 1000)
        } else {
          this.$message.error('请完善必填信息')
          return false
        }
      })
    }
  }
}
</script>

<style lang="less" scoped>
.agent-auth-dialog {
  .agent-info {
    display: flex;
    align-items: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 24px;

    .agent-icon {
      margin-right: 16px;

      .agent-logo {
        width: 48px;
        height: 48px;
        border-radius: 8px;
        object-fit: cover;
      }

      .default-logo {
        width: 48px;
        height: 48px;
        background: #e8f4ff;
        border-radius: 8px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: #409eff;
        font-size: 24px;
      }
    }

    .agent-details {
      .agent-name {
        margin: 0 0 4px 0;
        font-size: 18px;
        font-weight: 600;
        color: #333;
      }

      .agent-desc {
        margin: 0;
        font-size: 14px;
        color: #666;
      }
    }
  }

  .auth-form {
    .auth-notice {
      background: #f0f9ff;
      border: 1px solid #b3d8ff;
      border-radius: 6px;
      padding: 12px 16px;
      margin-bottom: 24px;
      display: flex;
      align-items: flex-start;

      i {
        color: #409eff;
        margin-right: 8px;
        margin-top: 2px;
        font-size: 16px;
      }

      span {
        color: #606266;
        font-size: 14px;
        line-height: 1.5;
      }
    }

    .el-form-item {
      margin-bottom: 20px;
    }

    .el-form-item__label {
      font-weight: 500;
      color: #606266;
    }

    .el-select,
    .el-date-picker {
      width: 100%;

      /deep/ .el-input__inner {
        border-radius: 6px;
        border: 1px solid #dcdfe6;
        transition: all 0.3s ease;

        &:focus {
          border-color: #409eff;
          box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
        }
      }
    }
  }

  .dialog-footer {
    text-align: right;
    padding-top: 20px;
    border-top: 1px solid #e8e8e8;

    .el-button {
      border-radius: 6px;
      font-weight: 500;
      padding: 10px 20px;

      &.el-button--primary {
        background: #409eff;
        border-color: #409eff;

        &:hover {
          background: #66b1ff;
          border-color: #66b1ff;
        }
      }
    }
  }
}

// 全局样式覆盖
::v-deep .agent-auth-dialog {
  .el-dialog__header {
    padding: 20px 20px 10px;
    border-bottom: 1px solid #e8e8e8;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
      color: #333;
    }
  }

  .el-dialog__body {
    padding: 20px;
  }

  .el-dialog__footer {
    padding: 0 20px 20px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .agent-auth-dialog {
    .agent-info {
      flex-direction: column;
      text-align: center;

      .agent-icon {
        margin-right: 0;
        margin-bottom: 12px;
      }
    }
  }

  ::v-deep .agent-auth-dialog {
    width: 90% !important;
    margin: 0 auto;

    .el-dialog__body {
      padding: 15px;
    }
  }
}
</style>
