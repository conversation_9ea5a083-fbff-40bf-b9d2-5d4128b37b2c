<template>
  <el-dialog
    title="新建智能体"
    :visible.sync="dialogVisible"
    width="50%"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="create-agent-dialog"
    @close="handleClose"
  >
    <div style="height: 400px;">
      <el-scrollbar style="height: 100%;">
        <el-form
          ref="createForm"
          :model="form"
          :rules="rules"
          label-width="100px"
          class="create-form"
        >
          <!-- 基础信息 -->
          <div class="form-section">
            <h4 class="section-title">基础信息</h4>

            <!-- LOGO上传 -->
            <el-form-item label="LOGO">
              <div class="logo-upload-container">
                <el-upload
                  class="logo-uploader"
                  action="#"
                  :show-file-list="false"
                  :before-upload="beforeLogoUpload"
                  accept="image/*"
                >
                  <img v-if="form.logoUrl" :src="form.logoUrl" class="logo-preview">
                  <div v-else class="logo-upload-placeholder">
                    <i class="el-icon-plus"></i>
                    <div class="upload-text">点击上传</div>
                  </div>
                </el-upload>
                <div class="upload-tips">
                  建议尺寸：200x200px，最大2MB
                </div>
              </div>
            </el-form-item>

            <!-- 应用名称 -->
            <el-form-item label="应用名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入智能体名称"
                maxlength="50"
                show-word-limit
              />
            </el-form-item>

            <!-- 智能体大类和子类 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="智能体大类" prop="category">
                  <el-select v-model="form.category" placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in categoryOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="智能体子类" prop="subCategory">
                  <el-select v-model="form.subCategory" placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in subCategoryOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 所属学科和学段 -->
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="所属学科" prop="subject">
                  <el-select v-model="form.subject" placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in subjectOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="所属学段" prop="type">
                  <el-select v-model="form.type" placeholder="请选择" style="width: 100%">
                    <el-option
                      v-for="item in typeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 智能体描述 -->
            <el-form-item label="智能体描述" prop="description">
              <el-input
                v-model="form.description"
                type="textarea"
                :rows="4"
                placeholder="请输入智能体描述..."
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </div>

          <!-- 高级配置 -->
          <div class="form-section">
            <h4 class="section-title">高级配置</h4>

            <!-- 功能模型 -->
            <el-form-item label="功能模型" prop="model">
              <el-select v-model="form.model" placeholder="请选择" style="width: 100%">
                <el-option
                  v-for="item in modelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>

            <!-- 系统提示词 -->
            <el-form-item label="系统提示词" prop="systemPrompt">
              <el-input
                v-model="form.systemPrompt"
                type="textarea"
                :rows="6"
                placeholder="请输入系统提示词..."
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>
          </div>
        </el-form>
      </el-scrollbar>
    </div>

    <div slot="footer" class="dialog-footer">
      <el-button @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        保存
      </el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'CreateAgentDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      loading: false,
      form: {
        name: '',
        category: '',
        subCategory: '',
        subject: '',
        type: '',
        description: '',
        model: '',
        systemPrompt: '',
        logoUrl: ''
      },
      rules: {
        name: [
          { required: true, message: '请输入应用名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        category: [
          { required: true, message: '请选择智能体大类', trigger: 'change' }
        ],
        subCategory: [
          { required: true, message: '请选择智能体子类', trigger: 'change' }
        ],
        subject: [
          { required: true, message: '请选择所属学科', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择所属学段', trigger: 'change' }
        ],
        description: [
          { required: true, message: '请输入智能体描述', trigger: 'blur' },
          { min: 10, max: 500, message: '长度在 10 到 500 个字符', trigger: 'blur' }
        ],
        model: [
          { required: true, message: '请选择功能模型', trigger: 'change' }
        ],
        systemPrompt: [
          { required: true, message: '请输入系统提示词', trigger: 'blur' },
          { min: 20, max: 2000, message: '长度在 20 到 2000 个字符', trigger: 'blur' }
        ]
      },
      // 选项数据
      categoryOptions: [
        { label: '适用助手', value: '适用助手' },
        { label: '课程作业', value: '课程作业' },
        { label: '数学设计', value: '数学设计' },
        { label: '学情分析', value: '学情分析' }
      ],
      subCategoryOptions: [
        { label: '智能助手', value: '智能助手' },
        { label: '作业助手', value: '作业助手' },
        { label: '公开课设计', value: '公开课设计' },
        { label: '数学设计', value: '数学设计' },
        { label: '适用推荐', value: '适用推荐' }
      ],
      subjectOptions: [
        { label: '数学', value: '数学' },
        { label: '英语', value: '英语' },
        { label: '物理', value: '物理' },
        { label: '化学', value: '化学' },
        { label: '语文', value: '语文' },
        { label: '历史', value: '历史' },
        { label: '地理', value: '地理' },
        { label: '生物', value: '生物' },
        { label: '音乐', value: '音乐' },
        { label: '计算机', value: '计算机' }
      ],
      typeOptions: [
        { label: '小学', value: '小学' },
        { label: '初中', value: '初中' },
        { label: '高中', value: '高中' }
      ],
      modelOptions: [
        { label: 'GPT-4', value: 'gpt-4' },
        { label: 'GPT-3.5', value: 'gpt-3.5' },
        { label: 'Claude-3', value: 'claude-3' },
        { label: '文心一言', value: 'wenxin' }
      ]
    }
  },
  computed: {
    dialogVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    // 重置表单
    resetForm() {
      this.form = {
        name: '',
        category: '',
        subCategory: '',
        subject: '',
        type: '',
        description: '',
        model: '',
        systemPrompt: '',
        logoUrl: ''
      }
      if (this.$refs.createForm) {
        this.$refs.createForm.clearValidate()
      }
    },

    // 关闭对话框
    handleClose() {
      this.resetForm()
      this.$emit('close')
    },

    // 取消
    handleCancel() {
      this.dialogVisible = false
    },

    // 确认创建
    handleConfirm() {
      this.$refs.createForm.validate((valid) => {
        if (valid) {
          this.loading = true

          // 模拟API调用延迟
          setTimeout(() => {
            this.loading = false
            this.$emit('confirm', { ...this.form })
            this.dialogVisible = false
            this.resetForm()
          }, 1000)
        } else {
          this.$message.error('请完善必填信息')
          return false
        }
      })
    },

    // LOGO上传前验证
    beforeLogoUpload(file) {
      const isImage = file.type.indexOf('image/') === 0
      const isLt2M = file.size / 1024 / 1024 < 2

      if (!isImage) {
        this.$message.error('只能上传图片文件!')
        return false
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 2MB!')
        return false
      }

      // 预览图片
      const reader = new FileReader()
      reader.onload = (e) => {
        this.form.logoUrl = e.target.result
      }
      reader.readAsDataURL(file)

      return false // 阻止自动上传
    }
  }
}
</script>

<style lang="less" scoped>
.create-agent-dialog {
  .el-scrollbar__wrap{
    overflow-x: hidden !important;
    overflow-y: scroll;
  }
  // .create-form {
  //   max-height: 60vh;
  //   overflow-y: auto;
  //   padding-right: 10px;

  //   .form-section {
  //     margin-bottom: 30px;

  //     .section-title {
  //       margin: 0 0 20px 0;
  //       padding-bottom: 10px;
  //       border-bottom: 1px solid #e8e8e8;
  //       font-size: 16px;
  //       font-weight: 600;
  //       color: #333;
  //     }
  //   }

    .logo-upload-container {
      display: flex;
      align-items: flex-start;
      gap: 16px;

      .logo-uploader {
        .logo-preview {
          width: 80px;
          height: 80px;
          border-radius: 6px;
          object-fit: cover;
          border: 1px solid #dcdfe6;
        }

        .logo-upload-placeholder {
          width: 80px;
          height: 80px;
          border: 2px dashed #d9d9d9;
          border-radius: 6px;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: border-color 0.3s;

          &:hover {
            border-color: #409eff;
          }

          i {
            font-size: 24px;
            color: #8c939d;
            margin-bottom: 4px;
          }

          .upload-text {
            font-size: 12px;
            color: #8c939d;
          }
        }
      }

  //     .upload-tips {
  //       font-size: 12px;
  //       color: #999;
  //       line-height: 1.4;
  //       margin-top: 8px;
  //     }
  //   }

  //   .el-form-item {
  //     margin-bottom: 20px;
  //   }

  //   .el-textarea {
  //     .el-textarea__inner {
  //       resize: vertical;
  //       min-height: 80px;
  //     }
  //   }
  // }

  // .dialog-footer {
  //   text-align: right;
  //   padding-top: 20px;
  //   border-top: 1px solid #e8e8e8;

  //   .el-button {
  //     margin-left: 10px;
  //   }
  // }
}

// 全局样式覆盖
// ::v-deep .create-agent-dialog {
//   .el-dialog__header {
//     padding: 20px 20px 10px;
//     border-bottom: 1px solid #e8e8e8;

//     .el-dialog__title {
//       font-size: 18px;
//       font-weight: 600;
//       color: #333;
//     }
//   }

//   .el-dialog__body {
//     padding: 20px;
//   }

//   .el-dialog__footer {
//     padding: 0 20px 20px;
//   }

//   .el-form-item__label {
//     font-weight: 500;
//     color: #606266;
//   }

//   .el-input__count {
//     color: #909399;
//     font-size: 12px;
//   }
// }

// // 响应式设计
// @media (max-width: 768px) {
//   .create-agent-dialog {
//     .create-form {
//       .logo-upload-container {
//         flex-direction: column;
//         align-items: center;
//         text-align: center;
//       }

//       .el-row {
//         .el-col {
//           margin-bottom: 10px;
//         }
//       }
//     }
//   }

//   ::v-deep .create-agent-dialog {
//     width: 90% !important;
//     margin: 0 auto;

//     .el-dialog__body {
//       padding: 15px;
//     }
//   }
// }
}
</style>

<style lang="less">
.create-agent-dialog {
  .el-scrollbar__wrap{
    overflow-x: hidden !important;
    overflow-y: scroll;
  }
}
</style>
