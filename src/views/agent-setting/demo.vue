<template>
  <div class="demo-page">
    <div class="demo-container">
      <h2>智能体授权弹窗演示</h2>
      <p>点击下方按钮查看授权弹窗效果</p>
      
      <div class="demo-buttons">
        <el-button type="primary" @click="showAuthDialog">
          <i class="el-icon-setting"></i>
          智能体授权
        </el-button>
      </div>

      <!-- 智能体授权对话框 -->
      <AgentAuthDialog
        :visible.sync="authDialogVisible"
        :agent-data="demoAgent"
        @confirm="handleAuthConfirm"
        @close="handleAuthClose"
      />
    </div>
  </div>
</template>

<script>
import AgentAuthDialog from './components/AgentAuthDialog.vue'

export default {
  name: 'AgentAuthDemo',
  components: {
    AgentAuthDialog
  },
  data() {
    return {
      authDialogVisible: false,
      demoAgent: {
        id: 1,
        name: '数学解题助手',
        description: '通用助手 - 智能问答',
        logo: '',
        defaultIcon: 'el-icon-edit'
      }
    }
  },
  methods: {
    showAuthDialog() {
      this.authDialogVisible = true
    },

    handleAuthConfirm(authData) {
      console.log('授权数据:', authData)
      this.$message.success('授权成功！')
    },

    handleAuthClose() {
      this.authDialogVisible = false
    }
  }
}
</script>

<style lang="less" scoped>
.demo-page {
  min-height: 100vh;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;

  .demo-container {
    background: white;
    border-radius: 12px;
    padding: 40px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 500px;
    width: 100%;

    h2 {
      margin: 0 0 16px 0;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }

    p {
      margin: 0 0 32px 0;
      color: #666;
      font-size: 16px;
    }

    .demo-buttons {
      .el-button {
        font-size: 16px;
        padding: 12px 24px;
        border-radius: 8px;

        i {
          margin-right: 8px;
        }
      }
    }
  }
}
</style>
